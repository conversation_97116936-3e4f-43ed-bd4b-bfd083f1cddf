from datetime import datetime, timezone
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean, JSON, Float
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB

from fastapi_app.db.base import Base


class ControlLog(Base):
    __tablename__ = "control_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    buoy_id = Column(Integer, ForeignKey("buoys.id"), nullable=False)
    command = Column(JSONB, nullable=False)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    status = Column(String, default="sent")  # 'sent', 'acknowledged'

    # 关系
    user = relationship("User", back_populates="control_logs")
    buoy = relationship("Buoy", back_populates="control_logs")


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    buoy_id = Column(Integer, ForeignKey("buoys.id"), nullable=True)
    type = Column(String, nullable=False)  # 'data_warning', 'system'
    message = Column(Text, nullable=False)
    read_status = Column(Boolean, default=False)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # 关系
    user = relationship("User", back_populates="notifications")
    buoy = relationship("Buoy", back_populates="notifications")


class UserSettings(Base):
    __tablename__ = "user_settings"

    user_id = Column(Integer, ForeignKey("users.id"), primary_key=True)
    theme = Column(String, default="light")  # "light", "dark"
    language = Column(String, default="zh_CN")
    notification_preferences = Column(JSONB, default={})
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    user = relationship("User", back_populates="settings")


class Feedback(Base):
    __tablename__ = "feedback"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    contact = Column(String, nullable=True)  # 联系方式（邮箱或其他）
    type = Column(String, nullable=False, default="other")  # 反馈类型：suggestion, problem, bug, other
    content = Column(Text, nullable=False)
    status = Column(String, nullable=False, default="pending")  # 状态：pending, processing, resolved
    reply = Column(Text, nullable=True)  # 管理员回复
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    user = relationship("User", back_populates="feedback")
